<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Admin</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }

    .container {
      max-width: 600px;
      margin: auto;
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 8px;
    }

    label {
      display: block;
      margin-bottom: 5px;
    }

    input[type="text"],
    input[type="email"],
    textarea {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }

    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    button:hover {
      background-color: #45a049;
    }

    #responseMessage {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }

    .success {
      background-color: #d4edda;
      color: #155724;
      border-color: #c3e6cb;
    }

    .error {
      background-color: #f8d7da;
      color: #721c24;
      border-color: #f5c6cb;
    }
  </style>
</head>

<body>
  <div class="container">
    <h2>Contact Admin</h2>
    <form id="contactForm">
      <label for="name">Name:</label>
      <input type="text" id="name" name="name" required>

      <label for="email">Your Email:</label>
      <input type="email" id="email" name="email" required>

      <label for="subject">Subject:</label>
      <input type="text" id="subject" name="subject" required>

      <label for="message">Message:</label>
      <textarea id="message" name="message" rows="5" required></textarea>

      <button type="submit">Send Email</button>
    </form>
    <div id="responseMessage"></div>
  </div>

  <script>
    document.getElementById('contactForm').addEventListener('submit', function (event) {
      event.preventDefault();

      const formData = new FormData(this);
      const responseMessage = document.getElementById('responseMessage');

      fetch('send_mail.php', {
        method: 'POST',
        body: formData
      })
        .then(response => response.json())
        .then(data => {
          responseMessage.style.display = 'block';
          if (data.success) {
            responseMessage.className = 'success';
            responseMessage.textContent = data.message;
            this.reset(); // Clear the form
          } else {
            responseMessage.className = 'error';
            responseMessage.textContent = data.message;
          }
        })
        .catch(error => {
          console.error('Error:', error);
          responseMessage.style.display = 'block';
          responseMessage.className = 'error';
          responseMessage.textContent = 'An unexpected error occurred. Please try again later.';
        });
    });
  </script>
</body>

</html>