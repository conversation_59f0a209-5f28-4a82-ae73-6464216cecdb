<?php
// Excel/CSV Manager for Contact Form Submissions
// This file provides functionality to view, download, and manage the contact submissions

$filename = 'contact_submissions.csv';

// Handle different actions
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'download':
        downloadFile();
        break;
    case 'view':
        viewData();
        break;
    case 'clear':
        clearData();
        break;
    case 'convert_to_excel':
        convertToExcel();
        break;
    default:
        showMenu();
        break;
}

function downloadFile() {
    global $filename;
    
    if (file_exists($filename)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filename));
        readfile($filename);
        exit;
    } else {
        echo "File not found.";
    }
}

function viewData() {
    global $filename;
    
    echo "<!DOCTYPE html><html><head><title>Contact Submissions</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .actions { margin-bottom: 20px; }
        .actions a { margin-right: 10px; padding: 5px 10px; background: #007cba; color: white; text-decoration: none; border-radius: 3px; }
        .actions a:hover { background: #005a87; }
    </style></head><body>";
    
    echo "<h1>Contact Form Submissions</h1>";
    echo "<div class='actions'>";
    echo "<a href='?action=download'>Download CSV</a>";
    echo "<a href='?action=convert_to_excel'>Download as Excel</a>";
    echo "<a href='?action=clear' onclick='return confirm(\"Are you sure you want to clear all data?\")'>Clear All Data</a>";
    echo "<a href='?'>Back to Menu</a>";
    echo "</div>";
    
    if (file_exists($filename)) {
        $file = fopen($filename, 'r');
        if ($file) {
            echo "<table>";
            $isHeader = true;
            while (($data = fgetcsv($file)) !== FALSE) {
                echo "<tr>";
                foreach ($data as $cell) {
                    if ($isHeader) {
                        echo "<th>" . htmlspecialchars($cell) . "</th>";
                    } else {
                        echo "<td>" . htmlspecialchars($cell) . "</td>";
                    }
                }
                echo "</tr>";
                $isHeader = false;
            }
            echo "</table>";
            fclose($file);
        }
    } else {
        echo "<p>No submissions found.</p>";
    }
    
    echo "</body></html>";
}

function clearData() {
    global $filename;
    
    if (file_exists($filename)) {
        unlink($filename);
        echo "Data cleared successfully. <a href='?'>Back to Menu</a>";
    } else {
        echo "No data to clear. <a href='?'>Back to Menu</a>";
    }
}

function convertToExcel() {
    global $filename;
    
    if (!file_exists($filename)) {
        echo "No data to convert.";
        return;
    }
    
    // Simple Excel XML format
    $excelFilename = 'contact_submissions.xls';
    
    $excel_content = '<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
<Worksheet ss:Name="Contact Submissions">
<Table>';
    
    $file = fopen($filename, 'r');
    if ($file) {
        while (($data = fgetcsv($file)) !== FALSE) {
            $excel_content .= '<Row>';
            foreach ($data as $cell) {
                $excel_content .= '<Cell><Data ss:Type="String">' . htmlspecialchars($cell) . '</Data></Cell>';
            }
            $excel_content .= '</Row>';
        }
        fclose($file);
    }
    
    $excel_content .= '</Table></Worksheet></Workbook>';
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $excelFilename . '"');
    echo $excel_content;
    exit;
}

function showMenu() {
    global $filename;
    
    $recordCount = 0;
    if (file_exists($filename)) {
        $lines = file($filename);
        $recordCount = count($lines) - 1; // Subtract 1 for header
        if ($recordCount < 0) $recordCount = 0;
    }
    
    echo "<!DOCTYPE html><html><head><title>Excel Manager</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 600px; }
        .menu-item { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .menu-item a { text-decoration: none; color: #007cba; font-weight: bold; }
        .menu-item a:hover { color: #005a87; }
        .stats { background: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style></head><body>";
    
    echo "<h1>Contact Form Excel Manager</h1>";
    echo "<div class='stats'><strong>Total Records:</strong> " . $recordCount . "</div>";
    
    echo "<div class='menu-item'>";
    echo "<a href='?action=view'>📊 View All Submissions</a><br>";
    echo "View all contact form submissions in a table format";
    echo "</div>";
    
    echo "<div class='menu-item'>";
    echo "<a href='?action=download'>📥 Download CSV File</a><br>";
    echo "Download the raw CSV file containing all submissions";
    echo "</div>";
    
    echo "<div class='menu-item'>";
    echo "<a href='?action=convert_to_excel'>📋 Download as Excel File</a><br>";
    echo "Convert and download data as an Excel (.xls) file";
    echo "</div>";
    
    echo "<div class='menu-item'>";
    echo "<a href='?action=clear' onclick='return confirm(\"Are you sure you want to clear all data? This cannot be undone.\")'>🗑️ Clear All Data</a><br>";
    echo "Remove all stored contact form submissions";
    echo "</div>";
    
    echo "</body></html>";
}
?>
