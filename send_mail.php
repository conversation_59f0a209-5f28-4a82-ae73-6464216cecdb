<?php
  $admin_email = '<EMAIL>';
  // $admin_email = '<EMAIL>';
  $email = $_POST['email'];

  // Send thank you email to the user
    $thankYouSubject = 'Thank you for showing interest in us!';
    $thankYouBody = "
      <html>
        <head>
          <title>Thank you for showing interest in us!</title>
        </head>
        <body>
          <h1>Thank you for showing interest in us!</h1>
          <p>Hello,</p>
          <p>We have received the following details:</p>
          <ul>
            <li>Email: " . $email . "</li>
            <li>Date: " . date('Y-m-d') . "</li>
            <li>Time: " . date('H:i') . "</li>
          <p>Best regards,</p>
          <p>Global Gateway</p>
        </body>
      </html>
    ";
    $thankYouHeaders = 'From: ' . $admin_email . "\r\n" .
                       'X-Mailer: PHP/' . phpversion() . "\r\n" .
                       'MIME-Version: 1.0' . "\r\n" .
                       'Content-Type: text/html; charset=UTF-8';

    mail($admin_email, $thankYouSubject, $thankYouBody, $thankYouHeaders);
    echo json_encode(array('success' => true));
?>
