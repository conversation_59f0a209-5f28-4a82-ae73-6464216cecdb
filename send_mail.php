<?php
  $admin_email = '<EMAIL>';

  // Get all form data
  $email = $_POST['email'] ?? '';
  $date = date('Y-m-d');
  $time = date('H:i:s');

  // Function to add data to Excel/CSV file
  function addToExcel($email, $date, $time) {
    $filename = 'contact_submissions.csv';
    $file_exists = file_exists($filename);

    // Open file for appending
    $file = fopen($filename, 'a');

    if ($file) {
      // Add header row if file is new
      if (!$file_exists) {
        $header = ['Date', 'Time', 'Email'];
        fputcsv($file, $header);
      }

      // Add the data row
      $data = [$date, $time, $email];
      fputcsv($file, $data);

      fclose($file);
      return true;
    }
    return false;
  }

  // Add data to Excel file before sending email
  $excel_success = addToExcel($email, $date, $time);

  // Check if Excel export was successful
  if (!$excel_success) {
    echo json_encode(array('success' => false, 'message' => 'Failed to save data to Excel file.'));
    exit;
  }

  $thankYouHeaders = 'From: ' . $admin_email . "\r\n" .
                     'X-Mailer: PHP/' . phpversion() . "\r\n" .
                     'MIME-Version: 1.0' . "\r\n" .
                     'Content-Type: text/html; charset=UTF-8';

  // Send notification email to admin
  $adminSubject = 'New Contact Form Submission';
  $adminBody = "
    <html>
      <head>
        <title>New Contact Form Submission</title>
      </head>
      <body>
        <h1>New Contact Form Submission</h1>
        <p><strong>Email:</strong> " . htmlspecialchars($email) . "</p>
        <p><strong>Date:</strong> " . $date . "</p>
        <p><strong>Time:</strong> " . $time . "</p>
        <hr>
        <p><em>This data has been automatically saved to contact_submissions.csv</em></p>
      </body>
    </html>
  ";

  $adminEmailSent = mail($admin_email, $adminSubject, $adminBody, $thankYouHeaders);

  echo json_encode(array('success' => true, 'message' => 'Thank you! Your message has been sent successfully.'));
?>
